import React, { useState, useCallback, useEffect } from 'react';
import { useKeyboardShortcut } from './hooks/useKeyboardShortcut';
import { useClipboardManager } from './hooks/useClipboardManager';
import { ClipboardItem } from './components/ClipboardItem';
import { Header } from './components/Header';
import { ClipboardIcon } from './components/Icons';

const App: React.FC = () => {
  const [isVisible, setIsVisible] = useState(false);
  const { history, syncClipboard, copyToClipboard, clearHistory, copiedItemId, deleteItem } = useClipboardManager();

  const toggleVisibility = useCallback(() => {
    setIsVisible(prev => !prev);
  }, []);

  useKeyboardShortcut(toggleVisibility, { key: 'c', ctrlKey: true, shiftKey: true });
  useKeyboardShortcut(() => isVisible && syncClipboard(), { key: 's', ctrlKey: true });
  useKeyboardShortcut(() => isVisible && setIsVisible(false), { key: 'Escape' });

  // When widget becomes visible, try syncing automatically
  useEffect(() => {
    if (isVisible) {
      syncClipboard();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isVisible]);

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
      if (e.target === e.currentTarget) {
          setIsVisible(false);
      }
  };

  if (!isVisible) {
    // Render a placeholder or nothing when the widget is hidden,
    // to avoid showing the "welcome" screen on every page load.
    // An empty fragment is clean and has no performance impact.
    return <></>;
  }

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      onClick={handleOverlayClick}
      aria-modal="true"
      role="dialog"
      style={{ pointerEvents: 'auto' }}
    >
      <div className="w-full max-w-md h-[70vh] max-h-[600px] flex flex-col bg-gray-800 rounded-2xl shadow-2xl overflow-hidden border border-gray-700">
        <Header 
          onSync={syncClipboard}
          onClear={clearHistory}
          onClose={() => setIsVisible(false)}
          itemCount={history.length}
        />
        
        <main className="flex-1 overflow-y-auto p-3">
          {history.length > 0 ? (
            <div className="space-y-2">
              {history.map(item => (
                <ClipboardItem
                  key={item.id}
                  item={item}
                  onCopy={copyToClipboard}
                  onDelete={deleteItem}
                  isCopied={copiedItemId === item.id}
                />
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full text-gray-500 text-center">
              <ClipboardIcon className="w-12 h-12 mb-4" />
              <h2 className="text-lg font-semibold text-gray-400">History is Empty</h2>
              <p>Copy some text to get started.</p>
              <p className="text-sm mt-2">Press <kbd className="px-1.5 py-0.5 bg-gray-700 rounded">Ctrl+S</kbd> to sync manually.</p>
            </div>
          )}
        </main>

        <footer className="p-2 text-center text-xs text-gray-500 border-t border-gray-700">
          Press <kbd className="px-1.5 py-0.5 bg-gray-700 rounded">Esc</kbd> to close.
        </footer>
      </div>
    </div>
  );
};

export default App;
