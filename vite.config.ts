import path from 'path';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },
      build: {
        rollupOptions: {
          input: {
            content: 'index.tsx'
          },
          output: {
            entryFileNames: 'content.js',
            chunkFileNames: 'content.js',
            assetFileNames: '[name].[ext]'
          }
        },
        outDir: 'dist',
        emptyOutDir: true,
        sourcemap: false,
        minify: false
      },
      publicDir: 'public'
    };
});
