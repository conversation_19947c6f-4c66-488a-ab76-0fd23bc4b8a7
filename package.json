{"name": "cliphistory---clipboard-manager", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run generate-icons && vite build", "build-only": "vite build", "generate-icons": "node -e \"const sharp = require('sharp'); const fs = require('fs'); const path = require('path'); async function createIcons() { const svgBuffer = fs.readFileSync('icon.svg'); const sizes = [16, 48, 128]; const iconsDir = path.join('public', 'icons'); if (!fs.existsSync(iconsDir)) { fs.mkdirSync(iconsDir, { recursive: true }); } for (const size of sizes) { await sharp(svgBuffer).resize(size, size).png().toFile(path.join(iconsDir, \\`icon\\${size}.png\\`)); } } createIcons().catch(console.error);\"", "preview": "vite preview"}, "dependencies": {"https": "^1.0.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@types/chrome": "^0.0.329", "@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "sharp": "^0.34.2", "typescript": "~5.7.2", "vite": "^6.2.0"}}