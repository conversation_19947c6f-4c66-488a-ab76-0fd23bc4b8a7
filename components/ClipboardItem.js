import React from 'https://esm.sh/react@19.1.0';
import htm from 'https://esm.sh/htm@3.1.1';
import { CopyIcon, CheckIcon, TrashIcon } from './Icons.js';

const html = htm.bind(React.createElement);

export const ClipboardItem = ({ item, onCopy, onDelete, isCopied }) => {
  const timeAgo = (timestamp) => {
    const seconds = Math.floor((new Date().getTime() - timestamp) / 1000);
    let interval = seconds / 31536000;
    if (interval > 1) return `${Math.floor(interval)}y ago`;
    interval = seconds / 2592000;
    if (interval > 1) return `${Math.floor(interval)}mo ago`;
    interval = seconds / 86400;
    if (interval > 1) return `${Math.floor(interval)}d ago`;
    interval = seconds / 3600;
    if (interval > 1) return `${Math.floor(interval)}h ago`;
    interval = seconds / 60;
    if (interval > 1) return `${Math.floor(interval)}m ago`;
    return `${Math.floor(seconds)}s ago`;
  };

  return html`
    <div className="group flex items-start p-3 bg-gray-700/50 hover:bg-gray-700 rounded-lg transition-colors duration-200">
      <div className="flex-1 min-w-0 pr-4">
        <p className="text-sm text-gray-200 break-words line-clamp-3">
          ${item.text}
        </p>
        <span className="text-xs text-gray-500">${timeAgo(item.timestamp)}</span>
      </div>
      <div className="flex items-center space-x-2">
        <button
          onClick=${() => onDelete(item.id)}
          className="p-1.5 rounded-full text-gray-400 hover:bg-red-900/50 hover:text-red-400 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          aria-label="Delete item"
        >
          <${TrashIcon} className="w-4 h-4" />
        </button>
        <button
          onClick=${() => onCopy(item)}
          className=${`p-2 rounded-full transition-all duration-200 ${
            isCopied
              ? 'bg-green-500 text-white'
              : 'bg-gray-600 text-gray-300 hover:bg-blue-600 hover:text-white'
          }`}
          aria-label=${isCopied ? 'Copied' : 'Copy item'}
        >
          ${isCopied ? html`<${CheckIcon} className="w-5 h-5" />` : html`<${CopyIcon} className="w-5 h-5" />`}
        </button>
      </div>
    </div>
  `;
};
