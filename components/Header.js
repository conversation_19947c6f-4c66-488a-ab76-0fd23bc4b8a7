import React from 'https://esm.sh/react@19.1.0';
import htm from 'https://esm.sh/htm@3.1.1';
import { SyncIcon, TrashIcon, CloseIcon, ClipboardIcon } from './Icons.js';

const html = htm.bind(React.createElement);

export const Header = ({ onSync, onClear, onClose, itemCount }) => {
  return html`
    <header className="flex items-center justify-between p-4 border-b border-gray-700">
      <div className="flex items-center space-x-3">
        <${ClipboardIcon} className="w-6 h-6 text-blue-400" />
        <h1 className="text-lg font-bold text-gray-100">ClipHistory</h1>
        ${itemCount > 0 && html`<span className="text-sm text-gray-400 bg-gray-700 px-2 py-0.5 rounded-full">${itemCount}</span>`}
      </div>
      <div className="flex items-center space-x-2">
        <button
          onClick=${onSync}
          className="p-2 rounded-full text-gray-400 hover:bg-gray-700 hover:text-white transition-colors"
          title="Sync from Clipboard (Ctrl+S)"
        >
          <${SyncIcon} className="w-5 h-5" />
        </button>
        <button
          onClick=${onClear}
          className="p-2 rounded-full text-gray-400 hover:bg-gray-700 hover:text-white transition-colors"
          title="Clear History"
        >
          <${TrashIcon} className="w-5 h-5" />
        </button>
        <button
          onClick=${onClose}
          className="p-2 rounded-full text-gray-400 hover:bg-gray-700 hover:text-white transition-colors"
          title="Close (Esc)"
        >
          <${CloseIcon} className="w-5 h-5" />
        </button>
      </div>
    </header>
  `;
};
