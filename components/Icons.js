import React from 'https://esm.sh/react@19.1.0';
import htm from 'https://esm.sh/htm@3.1.1';

const html = htm.bind(React.createElement);

export const SyncIcon = ({ className = 'w-6 h-6' }) => html`
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth=${1.5} stroke="currentColor" className=${className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0011.664 0M2.985 19.644A8.25 8.25 0 0114.648 8.02l-3.182-3.182m0 0h-4.992v4.992" />
  </svg>
`;

export const CopyIcon = ({ className = 'w-6 h-6' }) => html`
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth=${1.5} stroke="currentColor" className=${className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M15.666 3.888A2.25 2.25 0 0013.5 2.25h-3c-1.03 0-1.9.693-2.166 1.638m7.332 0c.055.194.084.4.084.612v3.042m-7.416 0c-.055-.194-.084-.4-.084-.612v-3.042a2.25 2.25 0 012.166-1.638h3c1.03 0 1.9.693 2.166 1.638m-7.332 0l-3.182 .025A2.25 2.25 0 004.5 5.25v13.5A2.25 2.25 0 006.75 21h10.5A2.25 2.25 0 0019.5 18.75V5.25a2.25 2.25 0 00-2.25-2.25h-3.182m-7.416 0v3.042A2.25 2.25 0 006.75 8.25h10.5a2.25 2.25 0 002.25-2.25v-3.042m0 0v-2.125a2.25 2.25 0 00-2.25-2.25h-3.182" />
  </svg>
`;

export const CheckIcon = ({ className = 'w-6 h-6' }) => html`
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth=${1.5} stroke="currentColor" className=${className}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M4.5 12.75l6 6 9-13.5" />
    </svg>
`;

export const TrashIcon = ({ className = 'w-6 h-6' }) => html`
  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth=${1.5} stroke="currentColor" className=${className}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.134-2.036-2.134H8.716c-1.126 0-2.036.954-2.036 2.134v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
  </svg>
`;

export const CloseIcon = ({ className = 'w-6 h-6' }) => html`
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth=${1.5} stroke="currentColor" className=${className}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
    </svg>
`;

export const ClipboardIcon = ({ className = 'w-6 h-6' }) => html`
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth=${1.5} stroke="currentColor" className=${className}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
    </svg>
`;
