import React from 'https://esm.sh/react@19.1.0';
import ReactDOM from 'https://esm.sh/react-dom@19.1.0/client';
import htm from 'https://esm.sh/htm@3.1.1';
import App from './App.js';

const html = htm.bind(React.createElement);

// This content script is injected into every page.
// It will create a root element and render the React app into it.

// 1. Create a container for the app to live in
const appContainer = document.createElement('div');
appContainer.id = 'cliphistory-react-root';
document.body.appendChild(appContainer);


// 2. Inject Tailwind CSS for styling
const tailwindScript = document.createElement('script');
tailwindScript.src = 'https://cdn.tailwindcss.com';
document.head.appendChild(tailwindScript);

// 3. Inject custom global styles, scoped to the app's container
const styleElement = document.createElement('style');
styleElement.textContent = `
  /* Custom scrollbar for the widget */
  #cliphistory-react-root ::-webkit-scrollbar {
    width: 8px;
  }
  #cliphistory-react-root ::-webkit-scrollbar-track {
    background: #2d3748; /* gray-800 */
  }
  #cliphistory-react-root ::-webkit-scrollbar-thumb {
    background: #4a5568; /* gray-600 */
    border-radius: 4px;
  }
  #cliphistory-react-root ::-webkit-scrollbar-thumb:hover {
    background: #718096; /* gray-500 */
  }
`;
document.head.appendChild(styleElement);


// 4. Render the React App into the container
const root = ReactDOM.createRoot(appContainer);
root.render(html`
  <${React.StrictMode}>
    <${App} />
  </${React.StrictMode}>
`);
