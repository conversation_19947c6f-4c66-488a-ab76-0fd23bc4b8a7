{"manifest_version": 3, "name": "ClipHistory - Clipboard Manager", "version": "1.0.1", "description": "A clipboard history application that allows users to view and manage their clipboard history. Access the widget with a shortcut and click an item to copy it back to your clipboard.", "permissions": ["storage", "clipboardRead", "clipboardWrite"], "icons": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "action": {"default_icon": {"16": "icons/icon16.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "default_title": "ClipHistory (Ctrl+Shift+C)"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_end"}]}