# ClipHistory - Clipboard Manager

A Chrome extension clipboard history application that allows users to view and manage their clipboard history. Access the widget with a shortcut and click an item to copy it back to your clipboard.

## Installation

**Prerequisites:** Node.js

1. <PERSON>lone or download this repository
2. Install dependencies: `npm install`
3. Build the extension: `npm run build` (this will automatically generate PNG icons and build the extension)
4. Open Chrome and go to `chrome://extensions/`
5. Enable "Developer mode" in the top right
6. Click "Load unpacked" and select the `dist` folder
7. The extension should now be installed and ready to use

**Note:** The build process automatically converts the SVG icon to the required PNG formats (16x16, 48x48, 128x128) that Chrome extensions need.

## Usage

- Press `Ctrl+Shift+C` to open the clipboard history widget
- Press `Ctrl+S` while the widget is open to manually sync clipboard
- Press `Esc` to close the widget
- Click on any item in the history to copy it back to your clipboard
- Use the sync button to refresh the clipboard history
- Use the clear button to delete all history items

## Permissions

This extension requires the following permissions:
- `storage`: To save clipboard history locally
- `clipboardRead`: To read from the system clipboard
- `clipboardWrite`: To write to the system clipboard

## Development

To modify the extension:
1. Make your changes to the source files
2. Run `npm run build` to rebuild
3. Reload the extension in Chrome extensions page
