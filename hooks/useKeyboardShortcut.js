import { useEffect, useCallback } from 'https://esm.sh/react@19.1.0';

export const useKeyboardShortcut = (callback, keyCombination) => {
  const handleKeyDown = useCallback(
    (event) => {
      const { key, ctrlKey, metaKey, shiftKey } = event;
      if (
        key.toLowerCase() === keyCombination.key.toLowerCase() &&
        (keyCombination.ctrlKey ? (ctrlKey || metaKey) : true) && // Use metaKey for MacOS Cmd key
        (keyCombination.shiftKey ? shiftKey : true)
      ) {
        event.preventDefault();
        callback();
      }
    },
    [callback, keyCombination]
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
};
