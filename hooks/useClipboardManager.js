import { useState, useCallback, useEffect } from 'https://esm.sh/react@19.1.0';

const STORAGE_KEY = 'clipboardHistory';
const MAX_HISTORY_ITEMS = 100;

export const useClipboardManager = () => {
  const [history, setHistory] = useState([]);
  const [copiedItemId, setCopiedItemId] = useState(null);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
      chrome.storage.local.get(STORAGE_KEY, (result) => {
        if (chrome.runtime.lastError) {
          console.error(`ClipHistory: Error loading history: ${chrome.runtime.lastError.message}`);
        } else if (result[STORAGE_KEY] && Array.isArray(result[STORAGE_KEY])) {
          setHistory(result[STORAGE_KEY]);
        }
        setIsLoaded(true);
      });
    } else {
      console.warn("ClipHistory: Not in extension context. History will not persist.");
      setIsLoaded(true);
    }
  }, []);

  useEffect(() => {
    if (isLoaded && typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
      const historyToSave = history.slice(0, MAX_HISTORY_ITEMS);
      chrome.storage.local.set({ [STORAGE_KEY]: historyToSave }, () => {
        if (chrome.runtime.lastError) {
          console.error(`ClipHistory: Error saving history: ${chrome.runtime.lastError.message}`);
        }
      });
    }
  }, [history, isLoaded]);

  const syncClipboard = useCallback(async () => {
    try {
      const text = await navigator.clipboard.readText();
      if (!text) return;
      
      setHistory(prev => {
        const filteredHistory = prev.filter(item => item.text !== text);
        const newItem = {
          id: `item-${Date.now()}`,
          text,
          timestamp: Date.now(),
        };
        return [newItem, ...filteredHistory];
      });

    } catch (error) {
        if (error instanceof Error && error.name === 'NotAllowedError') {
             // This can happen if the page is not in focus. Fail silently.
        } else {
             console.error("ClipHistory: Failed to read from clipboard.", error);
        }
    }
  }, []);

  const copyToClipboard = useCallback((item) => {
    navigator.clipboard.writeText(item.text).then(() => {
      setCopiedItemId(item.id);
      setTimeout(() => setCopiedItemId(null), 1500);
      setHistory(prev => [item, ...prev.filter(h => h.id !== item.id)]);
    }).catch(err => {
      console.error("ClipHistory: Failed to copy text: ", err);
    });
  }, []);

  const clearHistory = useCallback(() => {
    if (window.confirm('Are you sure you want to clear the entire clipboard history? This cannot be undone.')) {
      setHistory([]);
    }
  }, []);
  
  const deleteItem = useCallback((id) => {
    setHistory(prev => prev.filter(item => item.id !== id));
  }, []);

  return { history, syncClipboard, copyToClipboard, clearHistory, copiedItemId, deleteItem };
};
