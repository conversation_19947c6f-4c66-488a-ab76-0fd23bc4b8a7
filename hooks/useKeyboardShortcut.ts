
import { useEffect, useCallback } from 'react';

export const useKeyboardShortcut = (callback: () => void, keyCombination: { key: string; ctrlKey?: boolean; metaKey?: boolean; shiftKey?: boolean }) => {
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      const { key, ctrlKey, metaKey, shiftKey } = event;
      if (
        key.toLowerCase() === keyCombination.key.toLowerCase() &&
        (keyCombination.ctrlKey ? (ctrlKey || metaKey) : true) && // Use metaKey for MacOS Cmd key
        (keyCombination.shiftKey ? shiftKey : true)
      ) {
        event.preventDefault();
        callback();
      }
    },
    [callback, keyCombination]
  );

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
};
